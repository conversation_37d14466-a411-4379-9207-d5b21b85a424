'use client'

import { useEffect } from 'react'
import { formatWeight } from '@/utils/weightUtils'
import type { ExerciseModel, RecommendationModel } from '@/types'

interface ExerciseInfoProps {
  currentExercise?: ExerciseModel
  recommendation?: RecommendationModel | null
  performancePercentage: number | null
}

export function ExerciseInfo({
  currentExercise,
  recommendation,
  performancePercentage,
}: ExerciseInfoProps) {
  // Temporarily disabled to reduce console noise
  // Log recommendation data for debugging (only when data changes)
  // useEffect(() => {
  //   if (process.env.NODE_ENV === 'development') {
  //     // eslint-disable-next-line no-console
  //     console.log('[ExerciseInfo] Recommendation data:', {
  //       exerciseId: currentExercise?.Id,
  //       exerciseLabel: currentExercise?.Label,
  //       isBodyweight: currentExercise?.IsBodyweight,
  //       hasRecommendation: !!recommendation,
  //       weight: recommendation?.Weight,
  //       weightLb: recommendation?.Weight?.Lb,
  //       weightIsNull: recommendation?.Weight === null,
  //       weightIsZero: recommendation?.Weight?.Lb === 0,
  //       reps: recommendation?.Reps,
  //       series: recommendation?.Series,
  //     })
  //   }
  // }, [currentExercise?.Id, recommendation])

  const getTargetDisplay = () => {
    if (currentExercise?.IsTimeBased) {
      return `${currentExercise?.Timer || 45} seconds`
    }

    const repsText = `${recommendation?.Reps || 8} reps`

    // For non-bodyweight exercises, always try to show weight
    if (!currentExercise?.IsBodyweight) {
      // Check for weight in multiple ways
      let weightLb = 0
      if (recommendation?.Weight && recommendation.Weight.Lb > 0) {
        weightLb = recommendation.Weight.Lb
      } else if (
        recommendation?.RecommendationInKg &&
        recommendation.RecommendationInKg > 0
      ) {
        // Fallback: use RecommendationInKg if Weight is missing
        weightLb = recommendation.RecommendationInKg * 2.20462 // Convert kg to lbs
      }

      if (weightLb > 0) {
        const weight = { Lb: weightLb, Kg: weightLb * 0.453592 }
        return `${repsText} × ${formatWeight(weight, 'lbs')}`
      }
      // Show message when weight recommendation is missing
      return repsText
    }

    // For bodyweight exercises, just show reps
    return repsText
  }

  return (
    <div className="px-4 py-6 bg-bg-secondary border-b border-brand-primary/10">
      <h2 className="text-2xl font-bold text-text-primary mb-2">
        {currentExercise?.Label || 'Exercise'}
      </h2>
      <div className="space-y-1">
        {/* Target info */}
        <p className="text-text-secondary">Target: {getTargetDisplay()}</p>

        {/* Show additional message if weight is missing for weighted exercises */}
        {!currentExercise?.IsBodyweight &&
          !currentExercise?.IsTimeBased &&
          (!recommendation?.Weight || recommendation.Weight.Lb === 0) &&
          (!recommendation?.RecommendationInKg ||
            recommendation.RecommendationInKg === 0) && (
            <p className="text-sm text-warning">No weight recommendation</p>
          )}

        {/* Performance percentage */}
        {performancePercentage !== null && (
          <p
            className={`font-medium ${
              performancePercentage >= 0 ? 'text-success' : 'text-error'
            }`}
          >
            {performancePercentage >= 0 ? '+' : ''}
            {performancePercentage}%
          </p>
        )}
      </div>
    </div>
  )
}
