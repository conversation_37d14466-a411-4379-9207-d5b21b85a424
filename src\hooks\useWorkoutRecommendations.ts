import { useCallback, useMemo } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore/index'
// import { recommendationCache } from '@/services/cache/recommendationCache'
import { logger } from '@/utils/logger'
import {
  getExerciseRecommendation,
  // generateRecommendationCacheKey,
} from '@/services/api/workout'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { getUserSettings } from '@/services/userSettings'
import type { RecommendationModel } from '@/types'

// Track pending recommendation requests to avoid duplicates
const pendingRecommendationRequests = new Map<
  number,
  Promise<RecommendationModel | null>
>()

// Clear pending requests for testing
export const clearPendingRequests = () => {
  pendingRecommendationRequests.clear()
}

export function useWorkoutRecommendations() {
  const {
    getCachedExerciseRecommendation,
    setCachedExerciseRecommendation,
    loadAllExerciseRecommendations,
    currentWorkout,
  } = useWorkoutStore()

  const loadRecommendation = useCallback(
    async (
      exerciseId: number,
      exerciseName: string
    ): Promise<RecommendationModel | null> => {
      // eslint-disable-next-line no-console
      console.log('🎯 [useWorkoutRecommendations] loadRecommendation called', {
        exerciseId,
        exerciseName,
        currentWorkoutId: currentWorkout?.Id,
        hasCurrentWorkout: !!currentWorkout,
      })

      logger.log('[loadRecommendation] Starting for exercise', {
        exerciseId,
        exerciseName,
      })

      const userEmail = getCurrentUserEmail()
      if (!userEmail) {
        // eslint-disable-next-line no-console
        console.error('❌ [useWorkoutRecommendations] No user email found')
        logger.warn('[loadRecommendation] No user email found')
        return null
      }

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`

      // Check cache first
      const cached = getCachedExerciseRecommendation(exerciseId)
      if (cached) {
        // eslint-disable-next-line no-console
        console.log(
          '💾 [useWorkoutRecommendations] Using cached recommendation',
          {
            exerciseId,
            cached,
          }
        )
        logger.log('[loadRecommendation] Using cached recommendation', {
          exerciseId,
        })
        return cached
      }

      // Check if a request is already pending
      const pending = pendingRecommendationRequests.get(exerciseId)
      if (pending) {
        // eslint-disable-next-line no-console
        console.log('⏳ [useWorkoutRecommendations] Request already pending', {
          exerciseId,
        })
        logger.log('[loadRecommendation] Request already pending', {
          exerciseId,
        })
        return pending
      }

      // Create the request promise
      const requestPromise = (async () => {
        try {
          // eslint-disable-next-line no-console
          console.log('🔄 [useWorkoutRecommendations] Fetching from API', {
            exerciseId,
            workoutId: currentWorkout?.Id,
          })
          logger.log('[loadRecommendation] Fetching from API', { exerciseId })

          // Get workout ID from current workout
          const workoutId = currentWorkout?.Id
          if (!workoutId) {
            // eslint-disable-next-line no-console
            console.error(
              '❌ [useWorkoutRecommendations] No current workout ID available'
            )
            logger.warn('[loadRecommendation] No current workout ID available')
            return null
          }

          // Find the exercise to get its SetStyle and IsFlexibility
          const exercise = currentWorkout.Exercises?.find(
            (ex) => ex.Id === exerciseId
          )
          if (!exercise) {
            // eslint-disable-next-line no-console
            console.error(
              `❌ [useWorkoutRecommendations] Exercise ${exerciseId} not found in current workout`,
              {
                availableExercises: currentWorkout.Exercises?.map((ex) => ({
                  id: ex.Id,
                  label: ex.Label,
                })),
              }
            )
            logger.warn(`Exercise ${exerciseId} not found in current workout`)
            return null
          }

          // eslint-disable-next-line no-console
          console.log('📋 [useWorkoutRecommendations] Exercise details', {
            exerciseId,
            label: exercise.Label,
            setStyle: exercise.SetStyle,
            isFlexibility: exercise.IsFlexibility,
            bodyPartId: exercise.BodyPartId,
          })

          // Get user settings for the recommendation request
          const userSettings = await getUserSettings()

          // eslint-disable-next-line no-console
          console.log(
            '👤 [useWorkoutRecommendations] User settings',
            userSettings
          )

          // Create the request object for getExerciseRecommendation following mobile app pattern
          const request = {
            Username: userEmail,
            ExerciseId: exerciseId,
            WorkoutId: workoutId,
            SetStyle: exercise.SetStyle || 'Normal', // Default to Normal if not specified
            IsFlexibility: exercise.IsFlexibility || false, // Default to false if not specified
            IsQuickMode: userSettings.isQuickMode,
            LightSessionDays: userSettings.lightSessionDays,
            SwapedExId: undefined, // Only set if exercise was swapped
            IsStrengthPhashe: userSettings.isStrengthPhase, // Note: API has typo
            IsFreePlan: userSettings.isFreePlan,
            IsFirstWorkoutOfStrengthPhase:
              userSettings.isFirstWorkoutOfStrengthPhase,
            VersionNo: 1, // API version number
          }

          // eslint-disable-next-line no-console
          console.log(
            '📤 [useWorkoutRecommendations] Calling getExerciseRecommendation with request:',
            request
          )

          const recommendation = await getExerciseRecommendation(request)

          // eslint-disable-next-line no-console
          console.log(
            '📥 [useWorkoutRecommendations] Received recommendation:',
            {
              exerciseId,
              hasRecommendation: !!recommendation,
              recommendation,
            }
          )

          if (recommendation) {
            // Update store cache
            setCachedExerciseRecommendation(exerciseId, recommendation)
            // Update recommendationCache
            // TODO: Fix recommendationCache.set signature
            // recommendationCache.set(cacheKey, recommendation)
          }

          return recommendation
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(
            '❌ [useWorkoutRecommendations] Failed to fetch recommendation',
            {
              exerciseId,
              error,
            }
          )
          logger.error('[loadRecommendation] Failed to fetch', {
            exerciseId,
            error,
          })
          return null
        } finally {
          // Remove from pending requests
          pendingRecommendationRequests.delete(exerciseId)
        }
      })()

      // Track the pending request
      pendingRecommendationRequests.set(exerciseId, requestPromise)

      return requestPromise
    },
    [
      getCachedExerciseRecommendation,
      setCachedExerciseRecommendation,
      currentWorkout,
    ]
  )

  const preloadRecommendations = useCallback(
    async (exercises: Array<{ Id: number; Label: string }>) => {
      logger.log('[preloadRecommendations] Starting for exercises', {
        count: exercises.length,
      })

      const promises = exercises.map((exercise) =>
        loadRecommendation(exercise.Id, exercise.Label)
      )

      const results = await Promise.allSettled(promises)

      const successful = results.filter(
        (r) => r.status === 'fulfilled' && r.value !== null
      ).length

      logger.log('[preloadRecommendations] Completed', {
        total: exercises.length,
        successful,
        failed: exercises.length - successful,
      })

      return successful
    },
    [loadRecommendation]
  )

  const invalidateRecommendation = useCallback(
    (exerciseId: number) => {
      const userEmail = getCurrentUserEmail()
      if (!userEmail) return

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`
      // TODO: Fix delete method
      // recommendationCache.delete(cacheKey)

      // Also clear from store
      setCachedExerciseRecommendation(exerciseId, null)
    },
    [setCachedExerciseRecommendation]
  )

  return useMemo(
    () => ({
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    }),
    [
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    ]
  )
}
