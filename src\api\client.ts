import axios, {
  AxiosInstance,
  AxiosError,
  InternalAxiosRequestConfig,
} from 'axios'
import { logger } from '@/utils/logger'
import { withCSRFToken } from '@/utils/csrf'
import { setTokenChangeCallback } from '@/utils/tokenManager'

// API base URL - use environment variable or default to production
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || 'https://drmuscle.azurewebsites.net'

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Send cookies with requests
})

// Auth token management
export const setAuthToken = (token: string) => {
  apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`
}

export const clearAuthToken = () => {
  delete apiClient.defaults.headers.common['Authorization']
}

// Set up callback for token changes from token manager
setTokenChangeCallback((token) => {
  if (token) {
    setAuthToken(token)
  } else {
    clearAuthToken()
  }
})

// Request interceptor - Add Authorization header for Dr. Muscle API
apiClient.interceptors.request.use(
  async (config) => {
    // The Dr. Muscle API requires Bearer token authentication
    // We need to get the token and add it to the Authorization header

    // Authorization header is set via setAuthToken() function
    // which is called from the authStore when user logs in

    // Add CSRF token for state-changing requests
    if (
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(
        config.method?.toUpperCase() || ''
      )
    ) {
      const headers = withCSRFToken(config.headers)
      if (typeof headers === 'object' && headers !== null) {
        Object.assign(config.headers, headers)
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for token refresh and error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean
      _retryCount?: number
    }

    // Handle 401 errors - token expired
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      // The Dr. Muscle API doesn't have a refresh endpoint
      // When we get a 401, the token has expired and the user needs to login again
      logger.warn('Authentication token expired, redirecting to login')

      // Clear the auth token from the client
      clearAuthToken()

      try {
        // Retry original request with new Authorization header
        return apiClient.request(originalRequest)
      } catch (refreshError) {
        // Handle 404 specifically - refresh endpoint doesn't exist
        if (
          refreshError instanceof AxiosError &&
          refreshError.response?.status === 404
        ) {
          logger.warn(
            'Token refresh endpoint not found. Dr. Muscle API may not support token refresh.'
          )
        }

        // Refresh failed, clear cookies and redirect to login
        try {
          await fetch('/api/auth/exchange', {
            method: 'DELETE',
            credentials: 'include', // Ensure cookies are sent/received
          })
        } catch {
          // Ignore cookie clearing errors
        }

        // Clear auth state and redirect
        clearAuthToken()
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }

      // Throw an error to stop the request chain
      const authError = new Error(
        'Authentication required. Please login again.'
      )
      authError.name = 'AuthenticationError'
      throw authError
    }

    // Handle network errors (no response means network error)
    if (!error.response) {
      logger.error('Network error details:', {
        code: error.code,
        message: error.message,
        config: error.config?.url,
        online: navigator.onLine,
      })

      // Check if we're online
      const isOnline = navigator.onLine

      // Provide more specific error messages
      let networkErrorMessage = 'Network connection failed'
      if (!isOnline) {
        networkErrorMessage =
          'No internet connection. Please check your network.'
      } else if (error.code === 'ECONNREFUSED') {
        networkErrorMessage =
          'Unable to connect to Dr. Muscle servers. Please try again later.'
      } else if (
        error.code === 'TIMEOUT' ||
        (error.message && error.message.includes('timeout'))
      ) {
        networkErrorMessage =
          'Connection timeout. Please check your internet speed.'
      } else if (error.code === 'ERR_NETWORK') {
        networkErrorMessage =
          'Network error. Please check your connection and try again.'
      }

      // Online but network error - retry with backoff
      if (!originalRequest._retryCount) {
        originalRequest._retryCount = 0
      }

      if (originalRequest._retryCount < 3 && !originalRequest._retry) {
        originalRequest._retryCount++

        // Exponential backoff
        const delay = Math.min(
          1000 * Math.pow(2, originalRequest._retryCount - 1),
          10000
        )

        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(apiClient.request(originalRequest))
          }, delay)
        })
      } else {
        // All retries failed, throw with specific message
        const enhancedError = new Error(networkErrorMessage)
        enhancedError.name = 'NetworkError'
        throw enhancedError
      }
    }

    // Handle other HTTP errors
    if (error.response) {
      const errorData = {
        status: error.response.status,
        data: error.response.data,
        url: error.config?.url,
        method: error.config?.method,
        requestData: error.config?.data,
      }

      // Special handling for 404 on exercise recommendations
      if (
        error.response.status === 404 &&
        error.config?.url?.includes('GetRecommendationForExercise')
      ) {
        logger.warn(
          'Exercise recommendation endpoint not available:',
          errorData
        )
      } else {
        logger.error('API error response:', errorData)
      }

      // Extract error message from response
      let errorMessage = 'An error occurred'
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const data = error.response.data as any

      // Log the exact error structure for debugging
      logger.log('[API Client] Error response structure:', {
        status: error.response.status,
        data,
        hasErrorDescription: !!data?.error_description,
        hasMessage: !!data?.message,
        hasError: !!data?.error,
      })
      if (data?.error_description) {
        errorMessage = data.error_description
      } else if (data?.message) {
        errorMessage = data.message
      } else if (data?.error) {
        errorMessage = data.error
      } else if (typeof data === 'string') {
        // Sometimes APIs return plain text error messages
        errorMessage = data

        // Handle specific "resource removed" errors for exercise sets
        if (
          data.includes('resource you are looking for has been removed') &&
          error.config?.url?.includes('GetExerciseSets')
        ) {
          // Return empty sets instead of throwing error
          return Promise.resolve({ data: { Data: [] } })
        }
      } else if (error.response.status === 400) {
        errorMessage =
          'Invalid credentials. Please check your email and password.'
      } else if (error.response.status === 404) {
        // Handle specific 404 cases gracefully
        const url = error.config?.url || ''

        if (url.includes('GetRecommendationForExercise')) {
          // Don't throw for missing recommendation endpoint - graceful degradation
          logger.log(`404 handled gracefully for GetRecommendationForExercise: ${url}`)
          return Promise.resolve({ data: { Data: null } })
        } else if (url.includes('GetExerciseSets')) {
          // Don't throw for missing exercise sets - return empty array
          logger.log(`404 handled gracefully for GetExerciseSets: ${url}`)
          return Promise.resolve({ data: { Data: [] } })
        }

        errorMessage = 'Service not found. Please try again later.'
      } else if (error.response.status >= 500) {
        errorMessage = 'Server error. Please try again later.'
      }

      const enhancedError = new Error(errorMessage)
      enhancedError.name = 'APIError'
      throw enhancedError
    }

    return Promise.reject(error)
  }
)
