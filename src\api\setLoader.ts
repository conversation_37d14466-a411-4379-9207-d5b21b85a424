/**
 * Progressive Set Loader
 *
 * Implements progressive loading of sets for exercises with retry logic,
 * caching, and batch loading support. This enables the app to show
 * exercises immediately while loading their sets in the background.
 */

import { workoutApi } from '@/api/workouts'
import type { WorkoutLogSerieModel } from '@/types'

interface CachedSets {
  sets: WorkoutLogSerieModel[]
  timestamp: number
}

interface BatchLoadResult {
  success: boolean
  sets: WorkoutLogSerieModel[]
  error: string | null
}

export class SetLoader {
  private cache: Map<number, CachedSets>

  private ttl: number = 5 * 60 * 1000 // 5 minutes

  private maxRetries: number = 3

  private baseDelay: number = 1000

  private maxDelay: number = 30000

  private jitterFactor: number = 0.2

  constructor() {
    this.cache = new Map()
  }

  /**
   * Load sets for a single exercise with retry logic and caching
   */
  async loadExerciseSets(
    exerciseId: number,
    forceRefresh: boolean = false
  ): Promise<WorkoutLogSerieModel[]> {
    // Check cache first unless force refresh
    if (!forceRefresh) {
      const cached = this.cache.get(exerciseId)
      if (cached && Date.now() - cached.timestamp < this.ttl) {
        return cached.sets
      }
    }

    // Load with retry logic
    const sets = await this.loadWithRetry(exerciseId)

    // Cache the result
    this.cache.set(exerciseId, {
      sets,
      timestamp: Date.now(),
    })

    return sets
  }

  /**
   * Batch load sets for multiple exercises
   */
  async batchLoadExerciseSets(
    exerciseIds: number[]
  ): Promise<Record<number, BatchLoadResult>> {
    const results: Record<number, BatchLoadResult> = {}

    // Process all exercises in parallel
    await Promise.all(
      exerciseIds.map(async (exerciseId) => {
        try {
          const sets = await this.loadExerciseSets(exerciseId)
          results[exerciseId] = {
            success: true,
            sets,
            error: null,
          }
        } catch (error) {
          results[exerciseId] = {
            success: false,
            sets: [],
            error: error instanceof Error ? error.message : 'Unknown error',
          }
        }
      })
    )
    return results
  }

  /**
   * Clear cache for a specific exercise
   */
  clearCache(exerciseId: number): void {
    this.cache.delete(exerciseId)
  }

  /**
   * Clear all cached data
   */
  clearAllCache(): void {
    this.cache.clear()
  }

  /**
   * Load sets with exponential backoff retry
   */
  private async loadWithRetry(
    exerciseId: number
  ): Promise<WorkoutLogSerieModel[]> {
    let lastError: Error | null = null

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        // eslint-disable-next-line no-await-in-loop
        const sets = await workoutApi.getExerciseSets(exerciseId)

        // Handle null/undefined responses
        if (!sets) {
          return []
        }

        return sets
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')

        // Don't retry certain types of errors
        const errorMessage = lastError.message.toLowerCase()
        const isNonRetryableError =
          errorMessage.includes('404') ||
          errorMessage.includes('not found') ||
          errorMessage.includes('resource you are looking for has been removed')

        if (isNonRetryableError) {
          // For non-retryable errors, return empty array instead of throwing
          console.log(`Non-retryable error for exercise ${exerciseId}: ${lastError.message}`)
          return []
        }

        // Don't retry on the last attempt
        if (attempt < this.maxRetries) {
          const delay = this.calculateBackoffDelay(attempt)
          // eslint-disable-next-line no-await-in-loop
          await this.sleep(delay)
        }
      }
    }

    // All retries failed
    throw lastError || new Error('Failed to load exercise sets')
  }

  /**
   * Calculate exponential backoff delay with jitter
   */
  private calculateBackoffDelay(attempt: number): number {
    // Exponential backoff: baseDelay * (2 ^ attempt)
    const exponentialDelay = Math.min(
      this.baseDelay * Math.pow(2, attempt),
      this.maxDelay
    )

    // Add jitter: ±20% of calculated delay
    const jitter = exponentialDelay * this.jitterFactor
    const jitterAmount = (Math.random() * 2 - 1) * jitter

    return Math.max(0, exponentialDelay + jitterAmount)
  }

  /**
   * Sleep for a given duration
   */
  // eslint-disable-next-line class-methods-use-this
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms)
    })
  }
}
