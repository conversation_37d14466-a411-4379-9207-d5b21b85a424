import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { AllSetsDisplay } from '../AllSetsDisplay'
import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'

// Mock the formatWeight utility
vi.mock('@/utils/weightUtils', () => ({
  formatWeight: (weight: { Lb: number; Kg: number }) => `${weight.Lb} lbs`,
}))

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.23 },
  WarmupsCount: 2,
  WarmUpReps1: 5,
  WarmUpReps2: 8,
  WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
  WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
  WarmUpsList: [
    { WarmUpReps: 5, WarmUpWeightSet: { Lb: 95, Kg: 43.09 } },
    { WarmUpReps: 8, WarmUpWeightSet: { Lb: 115, Kg: 52.16 } },
  ],
  IsNormalSets: true,
  NbPauses: 0,
  NbRepsPauses: 0,
  // Add other required fields with default values
  OneRMProgress: 0,
  RecommendationInKg: 0,
  OneRMPercentage: 0,
  RpRest: 0,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Max: { Lb: 200, Kg: 90.72 },
  Min: { Lb: 50, Kg: 22.68 },
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
  FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {} as any,
  MinReps: 8,
  MaxReps: 12,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: true,
  isBandsAvailable: false,
  Speed: 0,
  IsManual: false,
  ReferenseReps: 10,
  ReferenseWeight: { Lb: 135, Kg: 61.23 },
  IsDropSet: false,
}

const mockCompletedSets: WorkoutLogSerieModel[] = [
  {
    Id: 1,
    ExerciseId: 1,
    Reps: 5,
    Weight: { Lb: 95, Kg: 43.09 },
    RIR: undefined,
    IsWarmups: true,
    SetNo: '1',
    IsNext: false,
    IsFinished: true,
  },
]

describe('AllSetsDisplay', () => {
  const mockOnSetClick = vi.fn()

  beforeEach(() => {
    mockOnSetClick.mockClear()
  })

  it('renders all sets correctly', () => {
    render(
      <AllSetsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={0}
        completedSets={[]}
        onSetClick={mockOnSetClick}
      />
    )

    // Should show title
    expect(screen.getByText('All Sets (5)')).toBeInTheDocument()

    // Should show warmup sets
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('W2')).toBeInTheDocument()

    // Should show work sets
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
  })

  it('shows completed sets correctly', () => {
    render(
      <AllSetsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={1}
        completedSets={mockCompletedSets}
        onSetClick={mockOnSetClick}
      />
    )

    // First warmup set should be marked as completed
    const completedSetButton = screen.getByLabelText(/Set W1: 5 reps at 95 lbs/)
    expect(completedSetButton).toHaveClass('border-green-200', 'bg-green-50')
  })

  it('highlights current set', () => {
    render(
      <AllSetsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={1}
        completedSets={[]}
        onSetClick={mockOnSetClick}
      />
    )

    // Second warmup set should be highlighted as current
    const currentSetButton = screen.getByLabelText(/Set W2: 8 reps at 115 lbs/)
    expect(currentSetButton).toHaveClass('border-blue-500', 'bg-blue-50')
  })

  it('calls onSetClick when a set is clicked', () => {
    render(
      <AllSetsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={0}
        completedSets={[]}
        onSetClick={mockOnSetClick}
      />
    )

    // Click on the first work set (index 2, after 2 warmups)
    const workSetButton = screen.getByLabelText(/Set 1: 10 reps at 135 lbs/)
    fireEvent.click(workSetButton)

    expect(mockOnSetClick).toHaveBeenCalledWith(2)
  })

  it('shows progress correctly', () => {
    render(
      <AllSetsDisplay
        recommendation={mockRecommendation}
        currentSetIndex={1}
        completedSets={mockCompletedSets}
        onSetClick={mockOnSetClick}
      />
    )

    // Should show progress: 1 of 5 sets completed
    expect(screen.getByText('Progress: 1 of 5 sets completed')).toBeInTheDocument()
    expect(screen.getByText('20%')).toBeInTheDocument()
  })

  it('handles empty recommendation gracefully', () => {
    const emptyRecommendation = { ...mockRecommendation, Series: 0 }
    
    render(
      <AllSetsDisplay
        recommendation={emptyRecommendation}
        currentSetIndex={0}
        completedSets={[]}
        onSetClick={mockOnSetClick}
      />
    )

    expect(screen.getByText('No sets available for this exercise')).toBeInTheDocument()
  })

  it('shows rest-pause sets correctly', () => {
    const restPauseRecommendation = {
      ...mockRecommendation,
      IsNormalSets: false,
      NbPauses: 2,
      NbRepsPauses: 6,
    }

    render(
      <AllSetsDisplay
        recommendation={restPauseRecommendation}
        currentSetIndex={2}
        completedSets={[]}
        onSetClick={mockOnSetClick}
      />
    )

    // Should show rest-pause indicators
    const restPauseIndicators = screen.getAllByText('Rest-Pause')
    expect(restPauseIndicators).toHaveLength(3) // 3 work sets
  })
})
