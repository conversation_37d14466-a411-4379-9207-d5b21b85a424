/**
 * Logger utility for Dr. Muscle X
 * Only logs in development mode to keep production clean
 */

const isDevelopment = process.env.NODE_ENV === 'development'

export const logger = {
  log: (...args: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.log(...args)
    // }
  },

  info: (...args: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.info(...args)
    // }
  },

  warn: (...args: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.warn(...args)
    // }
  },

  error: (...args: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // Always log errors, even in production
    // // eslint-disable-next-line no-console
    // console.error(...args)
  },

  debug: (...args: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.debug(...args)
    // }
  },

  group: (label: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.group(label)
    // }
  },

  groupEnd: () => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.groupEnd()
    // }
  },

  table: (data: unknown) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.table(data)
    // }
  },

  time: (label: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.time(label)
    // }
  },

  timeEnd: (label: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.timeEnd(label)
    // }
  },
}
